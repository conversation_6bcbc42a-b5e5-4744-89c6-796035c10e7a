import 'package:json_annotation/json_annotation.dart';

part 'chart_of_accounts.g.dart';

/// نموذج دليل الحسابات - Chart of Accounts Model
/// يمثل الحسابات المحاسبية في النظام
@JsonSerializable()
class ChartOfAccounts {
  final String id;
  final String accountCode;    // رمز الحساب
  final String accountNameAr;  // اسم الحساب بالعربية
  final String accountNameEn;  // اسم الحساب بالإنجليزية
  final String accountType;    // نوع الحساب (asset, liability, equity, revenue, expense)
  final String? parentId;      // معرف الحساب الأب
  final int level;             // مستوى الحساب في الهيكل الشجري
  final bool isActive;         // هل الحساب نشط
  final bool isSystem;         // هل هو حساب نظام (لا يمكن حذفه)
  final double balance;        // رصيد الحساب
  final String? description;   // وصف الحساب
  final String? notes;         // ملاحظات
  final String createdBy;      // من أنشأ الحساب
  final DateTime createdAt;    // تاريخ الإنشاء
  final DateTime? updatedAt;   // تاريخ التحديث
  final DateTime? deletedAt;   // تاريخ الحذف (soft delete)
  final bool isSynced;         // هل تم المزامنة

  const ChartOfAccounts({
    required this.id,
    required this.accountCode,
    required this.accountNameAr,
    required this.accountNameEn,
    required this.accountType,
    this.parentId,
    this.level = 1,
    this.isActive = true,
    this.isSystem = false,
    this.balance = 0.0,
    this.description,
    this.notes,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.isSynced = false,
  });

  /// إنشاء حساب من JSON
  factory ChartOfAccounts.fromJson(Map<String, dynamic> json) =>
      _$ChartOfAccountsFromJson(json);

  /// تحويل الحساب إلى JSON
  Map<String, dynamic> toJson() => _$ChartOfAccountsToJson(this);

  /// إنشاء حساب من Map قاعدة البيانات
  factory ChartOfAccounts.fromMap(Map<String, dynamic> map) {
    return ChartOfAccounts(
      id: map['id'] as String,
      accountCode: map['account_code'] as String,
      accountNameAr: map['account_name_ar'] as String,
      accountNameEn: map['account_name_en'] as String,
      accountType: map['account_type'] as String,
      parentId: map['parent_id'] as String?,
      level: (map['level'] as int?) ?? 1,
      isActive: (map['is_active'] as int?) == 1,
      isSystem: (map['is_system'] as int?) == 1,
      balance: (map['balance'] as num?)?.toDouble() ?? 0.0,
      description: map['description'] as String?,
      notes: map['notes'] as String?,
      createdBy: map['created_by'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] != null
          ? DateTime.parse(map['updated_at'] as String)
          : null,
      deletedAt: map['deleted_at'] != null
          ? DateTime.parse(map['deleted_at'] as String)
          : null,
      isSynced: (map['is_synced'] as int) == 1,
    );
  }

  /// تحويل الحساب إلى Map لقاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'account_code': accountCode,
      'account_name_ar': accountNameAr,
      'account_name_en': accountNameEn,
      'account_type': accountType,
      'parent_id': parentId,
      'level': level,
      'is_active': isActive ? 1 : 0,
      'is_system': isSystem ? 1 : 0,
      'balance': balance,
      'description': description,
      'notes': notes,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
    };
  }

  /// نسخ الحساب مع تعديل بعض الخصائص
  ChartOfAccounts copyWith({
    String? id,
    String? accountCode,
    String? accountNameAr,
    String? accountNameEn,
    String? accountType,
    String? parentId,
    int? level,
    bool? isActive,
    bool? isSystem,
    double? balance,
    String? description,
    String? notes,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deletedAt,
    bool? isSynced,
  }) {
    return ChartOfAccounts(
      id: id ?? this.id,
      accountCode: accountCode ?? this.accountCode,
      accountNameAr: accountNameAr ?? this.accountNameAr,
      accountNameEn: accountNameEn ?? this.accountNameEn,
      accountType: accountType ?? this.accountType,
      parentId: parentId ?? this.parentId,
      level: level ?? this.level,
      isActive: isActive ?? this.isActive,
      isSystem: isSystem ?? this.isSystem,
      balance: balance ?? this.balance,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deletedAt: deletedAt ?? this.deletedAt,
      isSynced: isSynced ?? this.isSynced,
    );
  }

  /// إنشاء حساب جديد
  factory ChartOfAccounts.create({
    required String id,
    required String accountCode,
    required String accountNameAr,
    required String accountNameEn,
    required String accountType,
        String? description,

    String? parentId,
    bool isActive = true,
    int level = 1,
    bool isSystem = false,
    double balance = 0.0,
    String? notes,
    required String createdBy,
  }) {
    final now = DateTime.now();
    return ChartOfAccounts(
      id: id,
      accountCode: accountCode,
      accountNameAr: accountNameAr,
      accountNameEn: accountNameEn,
      accountType: accountType,
      parentId: parentId,
      level: level,
      isSystem: isSystem,
      balance: balance,
      description: description,
      notes: notes,
      createdBy: createdBy,
      createdAt: now,
      isSynced: false,
    );
  }

  /// التحقق من صحة بيانات الحساب
  bool get isValid {
    return id.isNotEmpty &&
        accountCode.isNotEmpty &&
        accountNameAr.isNotEmpty &&
        accountType.isNotEmpty &&
        createdBy.isNotEmpty &&
        level > 0;
  }

  /// هل الحساب محذوف؟
  bool get isDeleted => deletedAt != null;

  /// هل الحساب جذري؟ (ليس له أب)
  bool get isRoot => parentId == null;

  /// هل الحساب مدين؟
  bool get isDebit => balance > 0;

  /// هل الحساب دائن؟
  bool get isCredit => balance < 0;

  /// الرصيد المطلق
  double get absoluteBalance => balance.abs();

  /// نوع الحساب بالعربية
  String get accountTypeArabic {
    switch (accountType.toLowerCase()) {
      case 'asset':
        return 'أصول';
      case 'liability':
        return 'خصوم';
      case 'equity':
        return 'حقوق الملكية';
      case 'revenue':
        return 'إيرادات';
      case 'expense':
        return 'مصروفات';
      default:
        return accountType;
    }
  }

  /// هل الحساب من نوع الأصول؟
  bool get isAsset => accountType.toLowerCase() == 'asset';

  /// هل الحساب من نوع الخصوم؟
  bool get isLiability => accountType.toLowerCase() == 'liability';

  /// هل الحساب من نوع حقوق الملكية؟
  bool get isEquity => accountType.toLowerCase() == 'equity';

  /// هل الحساب من نوع الإيرادات؟
  bool get isRevenue => accountType.toLowerCase() == 'revenue';

  /// هل الحساب من نوع المصروفات؟
  bool get isExpense => accountType.toLowerCase() == 'expense';

  /// تحديث الرصيد
  ChartOfAccounts updateBalance(double newBalance) {
    return copyWith(
      balance: newBalance,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// إضافة مبلغ للرصيد
  ChartOfAccounts addToBalance(double amount) {
    return updateBalance(balance + amount);
  }

  /// خصم مبلغ من الرصيد
  ChartOfAccounts subtractFromBalance(double amount) {
    return updateBalance(balance - amount);
  }

  /// تفعيل الحساب
  ChartOfAccounts activate() {
    return copyWith(
      isActive: true,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// إلغاء تفعيل الحساب
  ChartOfAccounts deactivate() {
    return copyWith(
      isActive: false,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
  }

  /// الاسم الكامل للحساب (رمز + اسم)
  String get fullName => '$accountCode - $accountNameAr';

  /// الاسم المختصر للعرض
  String get displayName => accountNameAr;

  @override
  String toString() {
    return 'ChartOfAccounts(id: $id, accountCode: $accountCode, accountNameAr: $accountNameAr, accountType: $accountType, balance: $balance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChartOfAccounts && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
