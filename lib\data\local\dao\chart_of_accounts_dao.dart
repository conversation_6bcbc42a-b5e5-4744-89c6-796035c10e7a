import '../../../core/constants/database_constants.dart';
import '../../models/chart_of_accounts.dart';
import 'base_dao.dart';

/// DAO لدليل الحسابات - Chart of Accounts Data Access Object
/// يدير عمليات قاعدة البيانات لدليل الحسابات
class ChartOfAccountsDao extends BaseDao<ChartOfAccounts> {
  @override
  String get tableName => DatabaseConstants.tableChartOfAccounts;

  @override
  ChartOfAccounts fromMap(Map<String, dynamic> map) => ChartOfAccounts.fromMap(map);

  @override
  Map<String, dynamic> toMap(ChartOfAccounts entity) => entity.toMap();

  /// الحصول على الحسابات حسب النوع - Get accounts by type
  Future<List<ChartOfAccounts>> getByType(String accountType) async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnChartOfAccountsType} = ? AND deleted_at IS NULL',
        whereArgs: [accountType],
        orderBy: '${DatabaseConstants.columnChartOfAccountsCode} ASC',
      );
    } catch (e) {
      print('خطأ في getByType: $e');
      return [];
    }
  }

  /// الحصول على الحسابات الرئيسية - Get parent accounts
  Future<List<ChartOfAccounts>> getParentAccounts() async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnChartOfAccountsParentId} IS NULL AND deleted_at IS NULL',
        orderBy: '${DatabaseConstants.columnChartOfAccountsCode} ASC',
      );
    } catch (e) {
      print('خطأ في getParentAccounts: $e');
      return [];
    }
  }

  /// الحصول على الحسابات الفرعية - Get child accounts
  Future<List<ChartOfAccounts>> getChildAccounts(String parentId) async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnChartOfAccountsParentId} = ? AND deleted_at IS NULL',
        whereArgs: [parentId],
        orderBy: '${DatabaseConstants.columnChartOfAccountsCode} ASC',
      );
    } catch (e) {
      print('خطأ في getChildAccounts: $e');
      return [];
    }
  }

  /// البحث عن حساب بالكود - Find account by code
  Future<ChartOfAccounts?> findByCode(String code) async {
    try {
      final accounts = await findWhere(
        where: '${DatabaseConstants.columnChartOfAccountsCode} = ? AND deleted_at IS NULL',
        whereArgs: [code],
        limit: 1,
      );
      return accounts.isNotEmpty ? accounts.first : null;
    } catch (e) {
      print('خطأ في findByCode: $e');
      return null;
    }
  }

  /// الحصول على الحسابات النشطة - Get active accounts
  Future<List<ChartOfAccounts>> getActiveAccounts() async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnChartOfAccountsIsActive} = 1 AND deleted_at IS NULL',
        orderBy: '${DatabaseConstants.columnChartOfAccountsCode} ASC',
      );
    } catch (e) {
      print('خطأ في getActiveAccounts: $e');
      return [];
    }
  }

  /// البحث في الحسابات - Search accounts
  Future<List<ChartOfAccounts>> searchAccounts(String query) async {
    try {
      return await findWhere(
        where: '''
          (${DatabaseConstants.columnChartOfAccountsName} LIKE ? 
          OR ${DatabaseConstants.columnChartOfAccountsCode} LIKE ? 
          OR ${DatabaseConstants.columnChartOfAccountsDescription} LIKE ?) 
          AND deleted_at IS NULL
        ''',
        whereArgs: ['%$query%', '%$query%', '%$query%'],
        orderBy: '${DatabaseConstants.columnChartOfAccountsCode} ASC',
      );
    } catch (e) {
      print('خطأ في searchAccounts: $e');
      return [];
    }
  }

  /// الحصول على رصيد الحساب - Get account balance
  Future<double> getAccountBalance(String accountId) async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT 
          COALESCE(SUM(CASE WHEN debit_account = ? THEN debit_amount ELSE 0 END), 0) as total_debit,
          COALESCE(SUM(CASE WHEN credit_account = ? THEN credit_amount ELSE 0 END), 0) as total_credit
        FROM ${DatabaseConstants.tableJournalEntries}
        WHERE (debit_account = ? OR credit_account = ?) 
        AND deleted_at IS NULL
      ''', [accountId, accountId, accountId, accountId]);

      final totalDebit = (result.first['total_debit'] as num?)?.toDouble() ?? 0.0;
      final totalCredit = (result.first['total_credit'] as num?)?.toDouble() ?? 0.0;

      // حساب الرصيد حسب نوع الحساب
      final account = await findById(accountId);
      if (account == null) return 0.0;

      // الحسابات المدينة (الأصول والمصروفات): المدين - الدائن
      // الحسابات الدائنة (الخصوم والإيرادات وحقوق الملكية): الدائن - المدين
      if (account.accountType == 'asset' || account.accountType == 'expense') {
        return totalDebit - totalCredit;
      } else {
        return totalCredit - totalDebit;
      }
    } catch (e) {
      print('خطأ في getAccountBalance: $e');
      return 0.0;
    }
  }

  /// التحقق من وجود كود الحساب - Check if account code exists
  Future<bool> codeExists(String code, {String? excludeAccountId}) async {
    try {
      String whereClause = '${DatabaseConstants.columnChartOfAccountsCode} = ? AND deleted_at IS NULL';
      List<dynamic> whereArgs = [code];
      
      if (excludeAccountId != null) {
        whereClause += ' AND ${DatabaseConstants.columnChartOfAccountsId} != ?';
        whereArgs.add(excludeAccountId);
      }

      final accounts = await findWhere(
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );
      
      return accounts.isNotEmpty;
    } catch (e) {
      print('خطأ في codeExists: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل الحساب - Activate/deactivate account
  Future<bool> setAccountActive(String accountId, bool isActive) async {
    try {
      final account = await findById(accountId);
      if (account == null) return false;

      final updatedAccount = account.copyWith(
        isActive: isActive,
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      return await update(accountId, updatedAccount);
    } catch (e) {
      print('خطأ في setAccountActive: $e');
      return false;
    }
  }

  /// إنشاء كود حساب تلقائي - Generate automatic account code
  Future<String> generateAccountCode(String accountType) async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT MAX(CAST(${DatabaseConstants.columnChartOfAccountsCode} AS INTEGER)) as max_code
        FROM $tableName 
        WHERE ${DatabaseConstants.columnChartOfAccountsType} = ? 
        AND deleted_at IS NULL
      ''', [accountType]);

      final maxCode = (result.first['max_code'] as int?) ?? 0;
      return (maxCode + 1).toString().padLeft(4, '0');
    } catch (e) {
      print('خطأ في generateAccountCode: $e');
      return '0001';
    }
  }
}
