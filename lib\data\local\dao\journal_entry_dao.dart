import '../../../core/constants/database_constants.dart';
import '../../models/journal_entry.dart';
import 'base_dao.dart';

/// DAO للقيود اليومية - Journal Entry Data Access Object
/// يدير عمليات قاعدة البيانات للقيود اليومية
class JournalEntryDao extends BaseDao<JournalEntry> {
  @override
  String get tableName => DatabaseConstants.tableJournalEntries;

  @override
  JournalEntry fromMap(Map<String, dynamic> map) => JournalEntry.fromMap(map);

  @override
  Map<String, dynamic> toMap(JournalEntry entity) => entity.toMap();

  /// الحصول على القيود حسب التاريخ - Get journal entries by date
  Future<List<JournalEntry>> getByDate(DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      return await findWhere(
        where: '''
          ${DatabaseConstants.columnJournalEntryDate} >= ? 
          AND ${DatabaseConstants.columnJournalEntryDate} < ? 
          AND deleted_at IS NULL
        ''',
        whereArgs: [startOfDay.toIso8601String(), endOfDay.toIso8601String()],
        orderBy: '${DatabaseConstants.columnJournalEntryDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByDate: $e');
      return [];
    }
  }

  /// الحصول على القيود حسب فترة زمنية - Get journal entries by date range
  Future<List<JournalEntry>> getByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      return await findWhere(
        where: '''
          ${DatabaseConstants.columnJournalEntryDate} >= ? 
          AND ${DatabaseConstants.columnJournalEntryDate} <= ? 
          AND deleted_at IS NULL
        ''',
        whereArgs: [startDate.toIso8601String(), endDate.toIso8601String()],
        orderBy: '${DatabaseConstants.columnJournalEntryDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByDateRange: $e');
      return [];
    }
  }

  /// الحصول على القيود حسب الحساب - Get journal entries by account
  Future<List<JournalEntry>> getByAccount(String accountId) async {
    try {
      return await findWhere(
        where: '''
          (${DatabaseConstants.columnJournalEntryDebitAccount} = ? 
          OR ${DatabaseConstants.columnJournalEntryCreditAccount} = ?) 
          AND deleted_at IS NULL
        ''',
        whereArgs: [accountId, accountId],
        orderBy: '${DatabaseConstants.columnJournalEntryDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByAccount: $e');
      return [];
    }
  }

  /// الحصول على القيود حسب النوع - Get journal entries by type
  Future<List<JournalEntry>> getByType(String type) async {
    try {
      return await findWhere(
        where: '${DatabaseConstants.columnJournalEntryReferenceType} = ? AND deleted_at IS NULL',
        whereArgs: [type],
        orderBy: '${DatabaseConstants.columnJournalEntryDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByType: $e');
      return [];
    }
  }

  /// الحصول على القيود حسب المرجع - Get journal entries by reference
  Future<List<JournalEntry>> getByReference(String referenceType, String referenceId) async {
    try {
      return await findWhere(
        where: '''
          ${DatabaseConstants.columnJournalEntryReferenceType} = ? 
          AND ${DatabaseConstants.columnJournalEntryReferenceId} = ? 
          AND deleted_at IS NULL
        ''',
        whereArgs: [referenceType, referenceId],
        orderBy: '${DatabaseConstants.columnJournalEntryDate} DESC',
      );
    } catch (e) {
      print('خطأ في getByReference: $e');
      return [];
    }
  }

  /// البحث في القيود - Search journal entries
  Future<List<JournalEntry>> searchEntries(String query) async {
    try {
      return await findWhere(
        where: '''
          (${DatabaseConstants.columnJournalEntryAmount} LIKE ? 
          OR ${DatabaseConstants.columnJournalEntryNotes} LIKE ?) 
          AND deleted_at IS NULL
        ''',
        whereArgs: ['%$query%', '%$query%'],
        orderBy: '${DatabaseConstants.columnJournalEntryDate} DESC',
      );
    } catch (e) {
      print('خطأ في searchEntries: $e');
      return [];
    }
  }

  /// الحصول على إجمالي المبالغ لفترة معينة - Get total amounts for period
  Future<Map<String, double>> getTotalAmountsForPeriod(DateTime startDate, DateTime endDate) async {
    try {
      final db = await database;
      final result = await db.rawQuery('''
        SELECT 
          SUM(${DatabaseConstants.columnJournalEntryDebitAccount}) as total_debit,
          SUM(${DatabaseConstants.columnJournalEntryCreditAccount}) as total_credit
        FROM $tableName 
        WHERE ${DatabaseConstants.columnJournalEntryDate} >= ? 
        AND ${DatabaseConstants.columnJournalEntryDate} <= ? 
        AND deleted_at IS NULL
      ''', [startDate.toIso8601String(), endDate.toIso8601String()]);

      return {
        'total_debit': (result.first['total_debit'] as num?)?.toDouble() ?? 0.0,
        'total_credit': (result.first['total_credit'] as num?)?.toDouble() ?? 0.0,
      };
    } catch (e) {
      print('خطأ في getTotalAmountsForPeriod: $e');
      return {'total_debit': 0.0, 'total_credit': 0.0};
    }
  }

  /// التحقق من توازن القيود - Validate journal entry balance
  Future<bool> validateBalance(String entryId) async {
    try {
      final entry = await findById(entryId);
      if (entry == null) return false;

      return entry.debitAccount == entry.creditAccount;
    } catch (e) {
      print('خطأ في validateBalance: $e');
      return false;
    }
  }

  /// الحصول على القيود غير المتوازنة - Get unbalanced entries
  Future<List<JournalEntry>> getUnbalancedEntries() async {
    try {
      final db = await database;
      final maps = await db.rawQuery('''
        SELECT * FROM $tableName 
        WHERE ${DatabaseConstants.columnJournalEntryDebitAccount} != ${DatabaseConstants.columnJournalEntryCreditAmount}
        AND deleted_at IS NULL
        ORDER BY ${DatabaseConstants.columnJournalEntryDate} DESC
      ''');

      return maps.map((map) => fromMap(map)).toList();
    } catch (e) {
      print('خطأ في getUnbalancedEntries: $e');
      return [];
    }
  }

  /// عكس القيد - Reverse journal entry
  Future<JournalEntry?> reverseEntry(String entryId, String reason) async {
    try {
      final originalEntry = await findById(entryId);
      if (originalEntry == null) return null;

      final reversedEntry = originalEntry.copyWith(
        id: null, // سيتم توليد معرف جديد
        debitAccount: originalEntry.creditAccount,
        creditAccount: originalEntry.debitAccount,
       
        notes: 'عكس القيد رقم ${originalEntry.id} - السبب: $reason',
        referenceType: 'reversal',
        referenceId: originalEntry.id,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isSynced: false,
      );

      final success = await insert(reversedEntry);
      return success ? reversedEntry : null;
    } catch (e) {
      print('خطأ في reverseEntry: $e');
      return null;
    }
  }
}
