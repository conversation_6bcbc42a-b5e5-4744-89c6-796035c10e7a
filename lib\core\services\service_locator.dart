import 'package:get_it/get_it.dart';
import 'package:sqflite_common/sqlite_api.dart';
import 'package:tijari_tech/data/local/database.dart';

// استيراد قواعد البيانات والـ DAOs
import '../../data/local/dao/user_dao.dart';
import '../../data/local/dao/journal_entry_dao.dart';
import '../../data/local/dao/chart_of_accounts_dao.dart';
import '../../data/local/dao/audit_log_dao.dart';
import '../../data/local/dao/settings_dao.dart';
import '../../data/local/dao/employee_dao.dart';
import '../../data/local/dao/bank_account_dao.dart';
import '../../data/local/dao/salary_dao.dart';

// استيراد الخدمات
import '../../services/accounting_service.dart';
import '../../services/journal_entry_service.dart';
import '../../services/general_ledger_service.dart';
import '../../services/chart_of_accounts_service.dart';
import '../../services/user_service.dart';
import '../../services/settings_service.dart';

/// مُحدد موقع الخدمات - Service Locator
/// يدير تسجيل وحقن التبعيات لجميع خدمات النظام
/// Manages dependency injection and registration for all system services
class ServiceLocator {
  static final GetIt _getIt = GetIt.instance;

  /// الحصول على مثيل GetIt - Get GetIt instance
  static GetIt get instance => _getIt;

  /// تهيئة جميع الخدمات - Initialize all services
  /// يجب استدعاء هذه الدالة عند بدء تشغيل التطبيق
  static Future<void> init() async {
    // تسجيل قاعدة البيانات - Register database
    _getIt.registerLazySingleton<DatabaseHelper>(() => DatabaseHelper());

    // تسجيل الـ DAOs - Register DAOs
    await _registerDAOs();

    // تسجيل الخدمات - Register services
    await _registerServices();
  }

  /// تسجيل الـ DAOs - Register DAOs
  static Future<void> _registerDAOs() async {
    final dbHelper = _getIt<DatabaseHelper>();

    // تسجيل جميع الـ DAOs
    _getIt.registerLazySingleton<UserDao>(() => UserDao());
    _getIt.registerLazySingleton<JournalEntryDao>(() => JournalEntryDao());
    _getIt
        .registerLazySingleton<ChartOfAccountsDao>(() => ChartOfAccountsDao());
    _getIt.registerLazySingleton<AuditLogDao>(() => AuditLogDao());
    _getIt.registerLazySingleton<SettingsDao>(() => SettingsDao());
    _getIt.registerLazySingleton<EmployeeDao>(() => EmployeeDao());
    _getIt.registerLazySingleton<BankAccountDao>(() => BankAccountDao());
    _getIt.registerLazySingleton<SalaryDao>(() => SalaryDao());
  }

  /// تسجيل الخدمات - Register services
  static Future<void> _registerServices() async {
    // الحصول على الـ DAOs المطلوبة
    final userDao = _getIt<UserDao>();
    final journalEntryDao = _getIt<JournalEntryDao>();
    final chartOfAccountsDao = _getIt<ChartOfAccountsDao>();
    final auditLogDao = _getIt<AuditLogDao>();
    final settingsDao = _getIt<SettingsDao>();

    // تسجيل خدمة المحاسبة الأساسية
    _getIt.registerLazySingleton<AccountingService>(
      () => AccountingService(
        journalEntryDao: journalEntryDao,
        chartOfAccountsDao: chartOfAccountsDao,
        auditLogDao: auditLogDao,
      ),
    );

    // تسجيل خدمة القيود اليومية
    _getIt.registerLazySingleton<JournalEntryService>(
      () => JournalEntryService(
        journalEntryDao: journalEntryDao,
        chartOfAccountsDao: chartOfAccountsDao,
        auditLogDao: auditLogDao,
        accountingService: _getIt<AccountingService>(),
      ),
    );

    // تسجيل خدمة دفتر الأستاذ العام
    _getIt.registerLazySingleton<GeneralLedgerService>(
      () => GeneralLedgerService(
        journalEntryDao: journalEntryDao,
        chartOfAccountsDao: chartOfAccountsDao,
        auditLogDao: auditLogDao,
      ),
    );

    // تسجيل خدمة دليل الحسابات
    _getIt.registerLazySingleton<ChartOfAccountsService>(
      () => ChartOfAccountsService(
        chartOfAccountsDao: chartOfAccountsDao,
        auditLogDao: auditLogDao,
      ),
    );

    // تسجيل خدمة المستخدمين
    _getIt.registerLazySingleton<UserService>(
      () => UserService(
        userDao: userDao,
        auditLogDao: auditLogDao,
      ),
    );

    // تسجيل خدمة الإعدادات
    _getIt.registerLazySingleton<SettingsService>(
      () => SettingsService(
        settingsDao: settingsDao,
        auditLogDao: auditLogDao,
      ),
    );
  }

  /// الحصول على خدمة - Get service
  static T get<T extends Object>() => _getIt<T>();

  /// التحقق من تسجيل خدمة - Check if service is registered
  static bool isRegistered<T extends Object>() => _getIt.isRegistered<T>();

  /// إعادة تعيين جميع الخدمات - Reset all services
  /// يستخدم للاختبارات أو إعادة التهيئة
  static Future<void> reset() async {
    await _getIt.reset();
  }

  /// تسجيل خدمة مخصصة - Register custom service
  static void registerCustomService<T extends Object>(T service) {
    if (!_getIt.isRegistered<T>()) {
      _getIt.registerSingleton<T>(service);
    }
  }

  /// إلغاء تسجيل خدمة - Unregister service
  static Future<void> unregister<T extends Object>() async {
    if (_getIt.isRegistered<T>()) {
      await _getIt.unregister<T>();
    }
  }
}

/// دوال مساعدة للوصول السريع للخدمات - Helper functions for quick service access

/// الحصول على خدمة المحاسبة - Get accounting service
AccountingService get accountingService =>
    ServiceLocator.get<AccountingService>();

/// الحصول على خدمة القيود اليومية - Get journal entry service
JournalEntryService get journalEntryService =>
    ServiceLocator.get<JournalEntryService>();

/// الحصول على خدمة دفتر الأستاذ العام - Get general ledger service
GeneralLedgerService get generalLedgerService =>
    ServiceLocator.get<GeneralLedgerService>();

/// الحصول على خدمة دليل الحسابات - Get chart of accounts service
ChartOfAccountsService get chartOfAccountsService =>
    ServiceLocator.get<ChartOfAccountsService>();

/// الحصول على خدمة المستخدمين - Get user service
UserService get userService => ServiceLocator.get<UserService>();

/// الحصول على خدمة الإعدادات - Get settings service
SettingsService get settingsService => ServiceLocator.get<SettingsService>();

/// الحصول على قاعدة البيانات - Get database helper
DatabaseHelper get databaseHelper => ServiceLocator.get<DatabaseHelper>();

/// الحصول على الـ DAOs - Get DAOs
UserDao get userDao => ServiceLocator.get<UserDao>();
JournalEntryDao get journalEntryDao => ServiceLocator.get<JournalEntryDao>();
ChartOfAccountsDao get chartOfAccountsDao =>
    ServiceLocator.get<ChartOfAccountsDao>();
AuditLogDao get auditLogDao => ServiceLocator.get<AuditLogDao>();
SettingsDao get settingsDao => ServiceLocator.get<SettingsDao>();
